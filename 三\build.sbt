name := "SparkHDFSDemo"

version := "1.0"

scalaVersion := "2.12.15"

val sparkVersion = "3.3.0"
val hadoopVersion = "3.3.4"

libraryDependencies ++= Seq(
  "org.apache.spark" %% "spark-core" % sparkVersion,
  "org.apache.spark" %% "spark-sql" % sparkVersion,
  "org.apache.hadoop" % "hadoop-client" % hadoopVersion,
  "org.apache.hadoop" % "hadoop-hdfs" % hadoopVersion
)

// 设置主类
mainClass in Compile := Some("SparkHDFSDemo")

// 合并策略，避免打包冲突
assemblyMergeStrategy in assembly := {
  case PathList("META-INF", xs @ _*) => MergeStrategy.discard
  case "reference.conf" => MergeStrategy.concat
  case x => MergeStrategy.first
}

// 排除一些可能冲突的依赖
assemblyExcludedJars in assembly := {
  val cp = (fullClasspath in assembly).value
  cp filter {_.data.getName == "slf4j-log4j12-1.7.16.jar"}
}
