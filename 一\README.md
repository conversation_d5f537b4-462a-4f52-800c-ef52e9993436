# 题目一：Spark JDBC连接MySQL数据库操作

## 项目描述
本项目实现了使用Spark通过JDBC连接MySQL数据库，完成DataFrame的各种操作。

## 功能要求
1. 在MySQL数据库中创建sparkdemo数据库和employee表
2. 表字段包括：id（工号，6位），name，age，gender（性别），salary（工资）
3. 预先建立10行数据，包含学号220918和姓名李佳颖
4. 使用DataFrame插入5行新数据
5. 完成以下操作：
   - 查询所有数据，输出时去除id字段
   - 筛选出age>19的记录
   - 将数据按照gender进行分组
   - 输出salary的平均值

## 环境要求
- Scala 2.12.15
- Apache Spark 3.3.0
- MySQL 8.0+
- MySQL Connector/J 8.0.33

## 运行步骤

### 1. 初始化MySQL数据库
```sql
-- 在MySQL中执行init_database.sql脚本
source init_database.sql;
```

### 2. 配置MySQL连接
修改SparkJDBCDemo.scala中的数据库连接信息：
- url: MySQL服务器地址和端口
- user: 数据库用户名
- password: 数据库密码

### 3. 编译和运行
```bash
# 使用sbt编译
sbt compile

# 运行程序
sbt run

# 或者打包后运行
sbt assembly
spark-submit --class SparkJDBCDemo target/scala-2.12/SparkJDBCDemo-assembly-1.0.jar
```

## 预期输出
程序将依次输出：
1. 现有的10行初始数据
2. 插入5行新数据的确认信息
3. 去除id字段的所有数据
4. age>19的筛选结果
5. 按gender分组的统计结果
6. salary的平均值
7. 按性别分组的平均工资

## 注意事项
- 确保MySQL服务正在运行
- 确保MySQL用户有足够的权限创建数据库和表
- 根据实际环境修改数据库连接参数
