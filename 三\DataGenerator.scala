import org.apache.spark.sql.SparkSession
import scala.util.Random

object DataGenerator {
  def main(args: Array[String]): Unit = {
    val spark = SparkSession.builder()
      .appName("Order Data Generator")
      .master("local[*]")
      .getOrCreate()

    import spark.implicits._

    // 商品种类列表
    val categories = Array(
      "电子产品", "服装鞋帽", "家居用品", "食品饮料", "图书音像",
      "运动户外", "美妆护肤", "母婴用品", "汽车用品", "办公用品",
      "手机数码", "家用电器", "珠宝首饰", "玩具乐器", "医疗保健"
    )

    val random = new Random()
    val outputPath = "hdfs://localhost:9000/orders/input"

    // 生成多个文件，每个文件包含50+条数据
    for (fileIndex <- 1 to 5) {
      val orders = (1 to (50 + random.nextInt(20))).map { i =>
        val orderId = f"ORD${fileIndex}%02d${i}%03d"
        val userId = f"USER${random.nextInt(1000)}%04d"
        val productId = f"PROD${random.nextInt(10000)}%05d"
        val category = categories(random.nextInt(categories.length))
        val amount = 10.0 + random.nextDouble() * 990.0 // 10-1000元随机金额
        
        (orderId, userId, productId, category, amount)
      }

      val ordersDF = orders.toDF("order_id", "user_id", "product_id", "product_category", "amount")
      
      // 写入HDFS
      ordersDF
        .coalesce(1)
        .write
        .mode("overwrite")
        .option("header", "false")
        .csv(s"$outputPath/orders_$fileIndex.csv")

      println(s"已生成文件 orders_$fileIndex.csv，包含 ${orders.length} 条记录")
    }

    // 生成一个汇总报告
    println("\n=== 数据生成汇总 ===")
    val allFiles = (1 to 5).map(i => s"$outputPath/orders_$i.csv")
    
    val allOrdersDF = spark.read
      .option("header", "false")
      .option("inferSchema", "true")
      .csv(allFiles: _*)
      .toDF("order_id", "user_id", "product_id", "product_category", "amount")

    println(s"总订单数: ${allOrdersDF.count()}")
    println("商品种类分布:")
    allOrdersDF.groupBy("product_category").count().orderBy($"count".desc).show()

    spark.stop()
  }
}
