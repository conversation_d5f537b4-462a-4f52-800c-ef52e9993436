import org.apache.spark.sql.{SparkSession, DataFrame}
import java.util.Properties

object SparkJDBCDemo {
  def main(args: Array[String]): Unit = {
    // 创建SparkSession
    val spark = SparkSession.builder()
      .appName("Spark JDBC MySQL Demo")
      .master("local[*]")
      .config("spark.sql.adaptive.enabled", "true")
      .getOrCreate()

    import spark.implicits._

    // MySQL连接配置
    val url = "*********************************************************************"
    val connectionProperties = new Properties()
    connectionProperties.put("user", "root")
    connectionProperties.put("password", "123456") // 请根据实际情况修改密码
    connectionProperties.put("driver", "com.mysql.cj.jdbc.Driver")

    try {
      // 读取现有数据
      val existingData = spark.read
        .jdbc(url, "employee", connectionProperties)

      println("=== 现有数据 ===")
      existingData.show()

      // 创建要插入的5行新数据
      val newData = Seq(
        (220919, "张三", 25, "男", 8000.0),
        (220920, "李四", 28, "女", 9500.0),
        (220921, "王五", 22, "男", 7500.0),
        (220922, "赵六", 30, "女", 10000.0),
        (220923, "孙七", 26, "男", 8500.0)
      ).toDF("id", "name", "age", "gender", "salary")

      // 插入新数据到MySQL
      newData.write
        .mode("append")
        .jdbc(url, "employee", connectionProperties)

      println("=== 成功插入5行新数据 ===")

      // 重新读取所有数据进行后续操作
      val allData = spark.read
        .jdbc(url, "employee", connectionProperties)

      // (1) 查询所有数据，输出时去除id字段
      println("=== (1) 查询所有数据，去除id字段 ===")
      val dataWithoutId = allData.drop("id")
      dataWithoutId.show()

      // (2) 筛选出age>19的记录
      println("=== (2) 筛选出age>19的记录 ===")
      val filteredData = allData.filter($"age" > 19)
      filteredData.show()

      // (3) 将数据按照gender进行分组
      println("=== (3) 按gender分组统计 ===")
      val groupedData = allData.groupBy("gender").count()
      groupedData.show()

      // (4) 输出salary的平均值
      println("=== (4) salary的平均值 ===")
      val avgSalary = allData.agg(avg("salary").alias("average_salary"))
      avgSalary.show()

      // 额外：按性别分组计算平均工资
      println("=== 按性别分组的平均工资 ===")
      val avgSalaryByGender = allData.groupBy("gender")
        .agg(avg("salary").alias("average_salary"))
      avgSalaryByGender.show()

    } catch {
      case e: Exception =>
        println(s"发生错误: ${e.getMessage}")
        e.printStackTrace()
    } finally {
      spark.stop()
    }
  }
}
