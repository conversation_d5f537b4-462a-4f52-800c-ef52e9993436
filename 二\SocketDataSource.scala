import java.io.PrintWriter
import java.net.ServerSocket
import scala.util.Random

object SocketDataSource {
  def main(args: Array[String]): Unit = {
    val port = 9999
    val serverSocket = new ServerSocket(port)
    
    println(s"Socket数据源启动，监听端口: $port")
    println("等待Spark Streaming连接...")
    
    // 等待客户端连接
    val socket = serverSocket.accept()
    val out = new PrintWriter(socket.getOutputStream(), true)
    
    println("Spark Streaming已连接，开始发送数据...")
    
    // 包含李佳颖姓名的示例数据
    val sampleData = Array(
      "hello 李佳颖 world spark streaming",
      "李佳颖 is learning big data",
      "hello world from 李佳颖",
      "spark streaming with 李佳颖",
      "李佳颖 hello scala programming",
      "big data processing by 李佳颖",
      "hello 李佳颖 distributed computing",
      "李佳颖 works on spark projects",
      "machine learning with 李佳颖 hello",
      "李佳颖 studies computer science hello",
      "data analysis by 李佳颖",
      "hello streaming data from 李佳颖",
      "李佳颖 implements spark applications",
      "real time processing hello 李佳颖",
      "李佳颖 hello big data analytics"
    )
    
    // 其他不包含姓名的数据（用于对比）
    val otherData = Array(
      "hello world without name",
      "spark streaming example",
      "big data processing",
      "machine learning algorithms",
      "distributed computing systems"
    )
    
    val random = new Random()
    
    try {
      // 持续发送数据
      while (true) {
        // 80%概率发送包含姓名的数据，20%发送其他数据
        val data = if (random.nextDouble() < 0.8) {
          sampleData(random.nextInt(sampleData.length))
        } else {
          otherData(random.nextInt(otherData.length))
        }
        
        out.println(data)
        println(s"发送数据: $data")
        
        // 每2-5秒发送一次数据
        Thread.sleep(2000 + random.nextInt(3000))
      }
    } catch {
      case e: Exception =>
        println(s"发生错误: ${e.getMessage}")
    } finally {
      socket.close()
      serverSocket.close()
      println("Socket数据源已关闭")
    }
  }
}
