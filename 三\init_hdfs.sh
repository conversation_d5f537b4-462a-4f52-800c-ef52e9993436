#!/bin/bash

# HDFS初始化脚本
echo "=== 初始化HDFS目录结构 ==="

# 创建输入目录
hdfs dfs -mkdir -p /orders/input
hdfs dfs -mkdir -p /orders/output

echo "HDFS目录创建完成"

# 检查目录是否创建成功
echo "=== 检查HDFS目录结构 ==="
hdfs dfs -ls /orders

# 创建本地测试数据文件
echo "=== 创建本地测试数据 ==="

# 创建订单数据文件1
cat > orders_1.csv << EOF
ORD0101001,USER0001,PROD00001,电子产品,299.99
ORD0101002,USER0002,PROD00002,服装鞋帽,159.50
ORD0101003,USER0003,PROD00003,家居用品,89.90
ORD0101004,USER0004,PROD00004,食品饮料,45.80
ORD0101005,USER0005,PROD00005,图书音像,25.60
ORD0101006,USER0006,PROD00006,运动户外,199.00
ORD0101007,USER0007,PROD00007,美妆护肤,128.50
ORD0101008,USER0008,PROD00008,母婴用品,89.90
ORD0101009,USER0009,PROD00009,汽车用品,356.70
ORD0101010,USER0010,PROD00010,办公用品,78.20
ORD0101011,USER0011,PROD00011,手机数码,1299.00
ORD0101012,USER0012,PROD00012,家用电器,899.50
ORD0101013,USER0013,PROD00013,珠宝首饰,2580.00
ORD0101014,USER0014,PROD00014,玩具乐器,156.80
ORD0101015,USER0015,PROD00015,医疗保健,89.90
ORD0101016,USER0016,PROD00016,电子产品,459.99
ORD0101017,USER0017,PROD00017,服装鞋帽,89.50
ORD0101018,USER0018,PROD00018,家居用品,129.90
ORD0101019,USER0019,PROD00019,食品饮料,35.80
ORD0101020,USER0020,PROD00020,图书音像,45.60
ORD0101021,USER0021,PROD00021,运动户外,299.00
ORD0101022,USER0022,PROD00022,美妆护肤,168.50
ORD0101023,USER0023,PROD00023,母婴用品,119.90
ORD0101024,USER0024,PROD00024,汽车用品,456.70
ORD0101025,USER0025,PROD00025,办公用品,98.20
ORD0101026,USER0026,PROD00026,手机数码,999.00
ORD0101027,USER0027,PROD00027,家用电器,1299.50
ORD0101028,USER0028,PROD00028,珠宝首饰,1580.00
ORD0101029,USER0029,PROD00029,玩具乐器,256.80
ORD0101030,USER0030,PROD00030,医疗保健,189.90
ORD0101031,USER0031,PROD00031,电子产品,359.99
ORD0101032,USER0032,PROD00032,服装鞋帽,79.50
ORD0101033,USER0033,PROD00033,家居用品,99.90
ORD0101034,USER0034,PROD00034,食品饮料,25.80
ORD0101035,USER0035,PROD00035,图书音像,35.60
ORD0101036,USER0036,PROD00036,运动户外,399.00
ORD0101037,USER0037,PROD00037,美妆护肤,228.50
ORD0101038,USER0038,PROD00038,母婴用品,149.90
ORD0101039,USER0039,PROD00039,汽车用品,556.70
ORD0101040,USER0040,PROD00040,办公用品,118.20
ORD0101041,USER0041,PROD00041,手机数码,1599.00
ORD0101042,USER0042,PROD00042,家用电器,1599.50
ORD0101043,USER0043,PROD00043,珠宝首饰,3580.00
ORD0101044,USER0044,PROD00044,玩具乐器,356.80
ORD0101045,USER0045,PROD00045,医疗保健,289.90
ORD0101046,USER0046,PROD00046,电子产品,659.99
ORD0101047,USER0047,PROD00047,服装鞋帽,119.50
ORD0101048,USER0048,PROD00048,家居用品,199.90
ORD0101049,USER0049,PROD00049,食品饮料,55.80
ORD0101050,USER0050,PROD00050,图书音像,65.60
EOF

# 上传到HDFS
echo "=== 上传测试数据到HDFS ==="
hdfs dfs -put orders_1.csv /orders/input/

# 验证上传
echo "=== 验证HDFS文件 ==="
hdfs dfs -ls /orders/input/
hdfs dfs -head /orders/input/orders_1.csv

echo "HDFS初始化完成！"
