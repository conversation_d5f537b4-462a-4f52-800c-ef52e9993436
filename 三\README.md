# 题目三：Spark应用程序HDFS文件处理

## 项目描述
本项目实现了一个完整的Spark应用程序，用于处理HDFS上的订单数据文件，按商品种类进行排序分析，找出最热门的10种商品，并将结果输出到HDFS。

## 功能要求
1. 在HDFS上创建多个订单数据文件
2. 每个文件包含至少50条数据
3. 每行数据格式：订单号,用户号,商品号,商品种类,金额
4. 读取所有文件中的数据
5. 按照商品种类降序排序
6. 显示最热门的10种商品
7. 将结果输出到HDFS新文件
8. 使用IDEA编写独立应用程序
9. 打包使用spark-submit提交到Spark独立调度器集群模式运行

## 环境要求
- Scala 2.12.15
- Apache Spark 3.3.0
- Hadoop 3.3.4
- HDFS集群
- Spark独立调度器集群

## 项目文件
- `SparkHDFSDemo.scala`: 主要的Spark应用程序
- `DataGenerator.scala`: 订单数据生成器
- `init_hdfs.sh`: HDFS初始化脚本
- `submit_spark.sh`: Spark应用提交脚本
- `build.sbt`: SBT构建配置文件

## 运行步骤

### 1. 初始化HDFS环境
```bash
# 给脚本执行权限
chmod +x init_hdfs.sh

# 执行HDFS初始化
./init_hdfs.sh
```

### 2. 生成测试数据（可选）
如果需要生成更多测试数据：
```bash
# 编译数据生成器
sbt compile

# 运行数据生成器
sbt "runMain DataGenerator"
```

### 3. 编译和打包应用
```bash
# 清理并编译打包
sbt clean assembly
```

### 4. 提交到Spark集群
```bash
# 给脚本执行权限
chmod +x submit_spark.sh

# 提交应用到集群
./submit_spark.sh
```

### 5. 手动提交（可选）
也可以手动提交应用：
```bash
spark-submit \
  --class SparkHDFSDemo \
  --master spark://localhost:7077 \
  --deploy-mode cluster \
  --driver-memory 1g \
  --executor-memory 1g \
  --executor-cores 2 \
  target/scala-2.12/SparkHDFSDemo-assembly-1.0.jar
```

## 数据格式
订单数据文件格式（CSV，无表头）：
```
订单号,用户号,商品号,商品种类,金额
ORD0101001,USER0001,PROD00001,电子产品,299.99
ORD0101002,USER0002,PROD00002,服装鞋帽,159.50
...
```

## 输出结果
程序将在HDFS的`/orders/output/`目录下生成结果文件，包含：
- product_category: 商品种类
- order_count: 订单数量
- total_amount: 总金额
- avg_amount: 平均金额

结果按订单数量降序排列，显示最热门的10种商品。

## 查看结果
```bash
# 查看输出目录
hdfs dfs -ls /orders/output/

# 查看结果文件内容
hdfs dfs -cat /orders/output/*.csv
```

## 注意事项
- 确保HDFS服务正在运行
- 确保Spark集群已启动
- 根据实际环境修改Spark Master地址
- 确保有足够的HDFS存储空间
- 检查网络连接和权限设置

## 故障排除
1. 如果HDFS连接失败，检查Hadoop配置
2. 如果Spark提交失败，检查集群状态
3. 如果内存不足，调整executor内存配置
4. 查看Spark Web UI监控应用执行状态
