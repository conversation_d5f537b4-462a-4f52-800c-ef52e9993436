题目一：在MySQL数据库中创建一个数据库sparkdemo，再数据库中创建员工表employee，表的字段包括：id（工号，6位，如220801），name，age，gender(性别)和salary（工资）。数据表事先建立十行数据，其中一行数据的工号和姓名必须是自己的学号和姓名。配置Spark通过JDBC连接数据库MySQL，编程实现利用DataFrame插入5行数据，并在spark程序中完成如下操作：
（1）查询所有数据，输出时去除id字段；
（2）筛选出age>19的记录；
（3）将数据按照gender进行分组；
（4）输出salary的平均值。

题目二：自定义Socket数据源（数据源内容要包含自己的名字），使用SparkStreaming编程实现监控某个端口号，获取该端口号内容，并对包含自己名字的字符串内容进行词频统计，并将某个指定的字符串如“hello”替换成“ok”，要求使用有状态的转换操作。

题目三：编写spark应用程序实现如下问题。在HDFS上的某个目录内创建多个文件，每个文件中的每一行内容均为五个字符串，分别为订单号,用户号，商品号，商品种类，金额。每个文件至少五十条数据，要求读取所有文件中的数据，并按照商品种类降序进行排序，显示最热门的10种商品，并将结果输出到HDFS一个新文件中。要求使用idea编写独立应用程序并打包使用spark-submit提交到spark的独立调度器集群模式中运行，最后显示结果文件的内容。

学号和姓名是：220918 李佳颖