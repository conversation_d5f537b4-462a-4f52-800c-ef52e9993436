#!/bin/bash

# Spark应用提交脚本

echo "=== 编译和打包Spark应用 ==="
sbt clean assembly

if [ $? -ne 0 ]; then
    echo "编译失败，请检查代码"
    exit 1
fi

echo "=== 提交Spark应用到集群 ==="

# Spark集群配置
SPARK_MASTER="spark://localhost:7077"  # 根据实际集群地址修改
APP_JAR="target/scala-2.12/SparkHDFSDemo-assembly-1.0.jar"

# 提交应用到Spark独立调度器集群模式
spark-submit \
  --class SparkHDFSDemo \
  --master $SPARK_MASTER \
  --deploy-mode cluster \
  --driver-memory 1g \
  --executor-memory 1g \
  --executor-cores 2 \
  --total-executor-cores 4 \
  --conf spark.sql.adaptive.enabled=true \
  --conf spark.sql.adaptive.coalescePartitions.enabled=true \
  $APP_JAR

if [ $? -eq 0 ]; then
    echo "=== Spark应用提交成功 ==="
    
    # 等待一段时间让应用执行完成
    echo "等待应用执行完成..."
    sleep 30
    
    # 查看输出结果
    echo "=== 查看HDFS输出结果 ==="
    hdfs dfs -ls /orders/output/
    
    echo "=== 显示结果文件内容 ==="
    hdfs dfs -cat /orders/output/*.csv
    
else
    echo "Spark应用提交失败"
    exit 1
fi
