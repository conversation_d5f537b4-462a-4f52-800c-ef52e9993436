import org.apache.spark.sql.{SparkSession, DataFrame}
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types._

object SparkHDFSDemo {
  def main(args: Array[String]): Unit = {
    // 创建SparkSession
    val spark = SparkSession.builder()
      .appName("Spark HDFS Order Analysis")
      .config("spark.sql.adaptive.enabled", "true")
      .getOrCreate()

    import spark.implicits._

    // HDFS路径配置
    val inputPath = "hdfs://localhost:9000/orders/input"
    val outputPath = "hdfs://localhost:9000/orders/output"

    try {
      println("=== 开始读取HDFS订单数据 ===")
      
      // 定义订单数据的Schema
      val orderSchema = StructType(Array(
        StructField("order_id", StringType, true),
        StructField("user_id", StringType, true),
        StructField("product_id", StringType, true),
        StructField("product_category", StringType, true),
        StructField("amount", DoubleType, true)
      ))

      // 读取HDFS上的所有订单文件
      val ordersDF = spark.read
        .option("header", "false")
        .option("delimiter", ",")
        .schema(orderSchema)
        .csv(s"$inputPath/*.csv")

      println(s"总共读取到 ${ordersDF.count()} 条订单记录")
      
      // 显示部分数据
      println("=== 订单数据样例 ===")
      ordersDF.show(10)

      // 按商品种类进行分组统计
      println("=== 按商品种类统计订单数量 ===")
      val categoryStats = ordersDF
        .groupBy("product_category")
        .agg(
          count("*").alias("order_count"),
          sum("amount").alias("total_amount"),
          avg("amount").alias("avg_amount")
        )
        .orderBy(desc("order_count"))

      categoryStats.show()

      // 获取最热门的10种商品种类
      println("=== 最热门的10种商品种类 ===")
      val top10Categories = categoryStats.limit(10)
      top10Categories.show()

      // 将结果写入HDFS
      println(s"=== 将结果写入HDFS: $outputPath ===")
      top10Categories
        .coalesce(1) // 合并为单个文件
        .write
        .mode("overwrite")
        .option("header", "true")
        .csv(outputPath)

      println("结果已成功写入HDFS")

      // 验证输出文件
      println("=== 验证输出文件内容 ===")
      val resultDF = spark.read
        .option("header", "true")
        .option("inferSchema", "true")
        .csv(outputPath)
      
      resultDF.show()

      // 额外分析：按商品种类和金额范围分析
      println("=== 商品种类详细分析 ===")
      val detailedAnalysis = ordersDF
        .groupBy("product_category")
        .agg(
          count("*").alias("订单数量"),
          sum("amount").alias("总金额"),
          avg("amount").alias("平均金额"),
          min("amount").alias("最小金额"),
          max("amount").alias("最大金额")
        )
        .orderBy(desc("订单数量"))

      detailedAnalysis.show(20, truncate = false)

    } catch {
      case e: Exception =>
        println(s"程序执行出错: ${e.getMessage}")
        e.printStackTrace()
    } finally {
      spark.stop()
    }
  }
}
