import org.apache.spark.SparkConf
import org.apache.spark.streaming.{Seconds, StreamingContext}
import org.apache.spark.streaming.dstream.DStream

object SparkStreamingDemo {
  def main(args: Array[String]): Unit = {
    // 创建Spark配置
    val conf = new SparkConf()
      .setAppName("Spark Streaming Socket Demo")
      .setMaster("local[2]") // 至少需要2个线程，一个接收数据，一个处理数据

    // 创建StreamingContext，批处理间隔为2秒
    val ssc = new StreamingContext(conf, Seconds(2))

    // 设置检查点目录，用于有状态转换
    ssc.checkpoint("checkpoint")

    // 连接到Socket数据源，监听localhost:9999端口
    val lines = ssc.socketTextStream("localhost", 9999)

    // 目标姓名
    val targetName = "李佳颖"

    // 筛选包含姓名的行
    val linesWithName = lines.filter(_.contains(targetName))

    // 将"hello"替换为"ok"
    val replacedLines = linesWithName.map(_.replaceAll("hello", "ok"))

    // 将每行分割成单词
    val words = replacedLines.flatMap(_.split("\\s+"))

    // 创建单词计数的DStream
    val wordCounts = words.map(word => (word, 1))

    // 使用updateStateByKey进行有状态的转换操作
    // 这个函数会维护每个单词的累计计数
    val runningCounts = wordCounts.updateStateByKey[Int](updateFunction _)

    // 定义状态更新函数
    def updateFunction(newValues: Seq[Int], runningCount: Option[Int]): Option[Int] = {
      val newCount = newValues.sum + runningCount.getOrElse(0)
      Some(newCount)
    }

    // 输出结果
    println("=== 开始监控Socket数据流 ===")
    println(s"正在监听localhost:9999端口...")
    println(s"筛选包含姓名'$targetName'的内容")
    println("将'hello'替换为'ok'")
    println("进行词频统计（有状态转换）")
    println("===========================")

    // 打印包含姓名的原始行
    linesWithName.foreachRDD { rdd =>
      if (!rdd.isEmpty()) {
        println(s"\n=== 包含'$targetName'的原始数据 ===")
        rdd.collect().foreach(println)
      }
    }

    // 打印替换后的行
    replacedLines.foreachRDD { rdd =>
      if (!rdd.isEmpty()) {
        println(s"\n=== 替换'hello'为'ok'后的数据 ===")
        rdd.collect().foreach(println)
      }
    }

    // 打印词频统计结果
    runningCounts.foreachRDD { rdd =>
      if (!rdd.isEmpty()) {
        println(s"\n=== 词频统计结果（累计） ===")
        val sortedCounts = rdd.collect().sortBy(-_._2) // 按频次降序排列
        sortedCounts.foreach { case (word, count) =>
          println(s"$word: $count")
        }
        println("========================")
      }
    }

    // 启动流处理
    ssc.start()
    
    println("Spark Streaming已启动，按Ctrl+C停止...")
    
    // 等待流处理结束
    ssc.awaitTermination()
  }
}
