name := "SparkJDBCDemo"

version := "1.0"

scalaVersion := "2.12.15"

val sparkVersion = "3.3.0"

libraryDependencies ++= Seq(
  "org.apache.spark" %% "spark-core" % sparkVersion,
  "org.apache.spark" %% "spark-sql" % sparkVersion,
  "mysql" % "mysql-connector-java" % "8.0.33"
)

// 设置主类
Compile / mainClass := Some("SparkJDBCDemo")

// 运行时的JVM选项
Compile / run / javaOptions ++= Seq(
  "-Xmx2g",
  "-Dlog4j.configuration=log4j.properties"
)

// 添加assembly插件
addSbtPlugin("com.eed3si9n" % "sbt-assembly" % "1.2.0")

// 合并策略，避免打包冲突
ThisBuild / assemblyMergeStrategy := {
  case PathList("META-INF", xs @ _*) => MergeStrategy.discard
  case "reference.conf" => MergeStrategy.concat
  case _ => MergeStrategy.first
}
