-- 创建数据库sparkdemo
CREATE DATABASE IF NOT EXISTS sparkdemo;
USE sparkdemo;

-- 创建员工表employee
CREATE TABLE IF NOT EXISTS employee (
    id INT(6) PRIMARY KEY COMMENT '工号，6位数字',
    name VARCHAR(50) NOT NULL COMMENT '姓名',
    age INT NOT NULL COMMENT '年龄',
    gender VARCHAR(10) NOT NULL COMMENT '性别',
    salary DECIMAL(10,2) NOT NULL COMMENT '工资'
);

-- 插入初始的10行数据，包含学号220918和姓名李佳颖
INSERT INTO employee (id, name, age, gender, salary) VALUES
(220918, '李佳颖', 20, '女', 7800.0),
(220801, '陈明', 23, '男', 8500.0),
(220802, '刘红', 25, '女', 9200.0),
(220803, '王强', 22, '男', 7600.0),
(220804, '张丽', 24, '女', 8800.0),
(220805, '李华', 26, '男', 9500.0),
(220806, '赵敏', 21, '女', 7200.0),
(220807, '孙伟', 27, '男', 10200.0),
(220808, '周芳', 23, '女', 8300.0),
(220809, '吴刚', 25, '男', 8900.0);

-- 查看插入的数据
SELECT * FROM employee;
