# 题目二：Spark Streaming Socket数据源词频统计

## 项目描述
本项目实现了自定义Socket数据源，使用Spark Streaming监控端口，对包含指定姓名的字符串进行词频统计和字符串替换，使用有状态的转换操作。

## 功能要求
1. 自定义Socket数据源，数据内容包含姓名"李佳颖"
2. 使用Spark Streaming监控9999端口
3. 筛选包含姓名"李佳颖"的字符串内容
4. 进行词频统计
5. 将"hello"替换成"ok"
6. 使用有状态的转换操作（updateStateByKey）

## 环境要求
- Scala 2.12.15
- Apache Spark 3.3.0
- Java 8+

## 项目文件
- `SparkStreamingDemo.scala`: 主要的Spark Streaming应用程序
- `SocketDataSource.scala`: Socket数据源模拟器
- `build.sbt`: SBT构建配置文件

## 运行步骤

### 1. 启动Socket数据源
首先在一个终端中启动Socket数据源：
```bash
# 编译数据源
sbt compile

# 运行Socket数据源
sbt "runMain SocketDataSource"
```

### 2. 启动Spark Streaming应用
在另一个终端中启动Spark Streaming应用：
```bash
# 运行Spark Streaming应用
sbt "runMain SparkStreamingDemo"

# 或者打包后运行
sbt assembly
spark-submit --class SparkStreamingDemo target/scala-2.12/SparkStreamingDemo-assembly-1.0.jar
```

## 程序特性

### 有状态转换操作
- 使用`updateStateByKey`维护每个单词的累计计数
- 设置检查点目录用于故障恢复
- 实现词频的持续累积统计

### 数据处理流程
1. 从Socket接收数据流
2. 筛选包含"李佳颖"的行
3. 将"hello"替换为"ok"
4. 分词处理
5. 词频统计（有状态）
6. 实时输出结果

### Socket数据源特点
- 自动生成包含"李佳颖"的测试数据
- 80%概率发送包含姓名的数据
- 20%概率发送其他数据用于对比
- 每2-5秒随机发送一次数据

## 预期输出
程序运行后将实时显示：
1. 包含"李佳颖"的原始数据
2. 替换"hello"为"ok"后的数据
3. 词频统计结果（按频次降序排列）
4. 累计的词频统计（有状态转换）

## 注意事项
- 确保9999端口未被占用
- 先启动Socket数据源，再启动Spark Streaming应用
- 程序会创建checkpoint目录用于状态管理
- 使用Ctrl+C停止程序运行
