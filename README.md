# 分布式计算框架作业

本项目包含三道关于Apache Spark分布式计算的题目实现，学号：220918，姓名：李佳颖。

## 项目结构

```
分布式计算框架-2/
├── 需求.md                    # 原始需求文档
├── README.md                  # 项目总体说明（本文件）
├── 一/                        # 题目一：Spark JDBC连接MySQL
│   ├── SparkJDBCDemo.scala    # 主程序
│   ├── init_database.sql      # MySQL初始化脚本
│   ├── build.sbt             # 构建配置
│   └── README.md             # 详细说明
├── 二/                        # 题目二：Spark Streaming
│   ├── SparkStreamingDemo.scala   # 主程序
│   ├── SocketDataSource.scala     # Socket数据源
│   ├── build.sbt                 # 构建配置
│   └── README.md                 # 详细说明
└── 三/                        # 题目三：Spark HDFS应用
    ├── SparkHDFSDemo.scala    # 主程序
    ├── DataGenerator.scala    # 数据生成器
    ├── init_hdfs.sh          # HDFS初始化脚本
    ├── submit_spark.sh       # Spark提交脚本
    ├── build.sbt             # 构建配置
    └── README.md             # 详细说明
```

## 题目概述

### 题目一：Spark JDBC连接MySQL数据库操作
- **目标**: 使用Spark通过JDBC连接MySQL，实现DataFrame操作
- **功能**: 
  - 创建sparkdemo数据库和employee表
  - 插入包含学号220918和姓名李佳颖的初始数据
  - 使用DataFrame插入5行新数据
  - 实现查询、筛选、分组、统计等操作
- **技术栈**: Spark SQL, JDBC, MySQL

### 题目二：Spark Streaming Socket数据源词频统计
- **目标**: 实现实时流数据处理和词频统计
- **功能**:
  - 自定义Socket数据源（包含姓名李佳颖）
  - 监控9999端口获取数据流
  - 筛选包含姓名的字符串进行词频统计
  - 字符串替换（hello→ok）
  - 使用有状态转换操作
- **技术栈**: Spark Streaming, Socket编程, 状态管理

### 题目三：Spark应用程序HDFS文件处理
- **目标**: 开发独立Spark应用处理HDFS大数据文件
- **功能**:
  - 在HDFS创建多个订单数据文件（每个文件50+条记录）
  - 读取所有文件进行商品种类分析
  - 按订单数量降序排序，找出最热门10种商品
  - 结果输出到HDFS新文件
  - 打包提交到Spark集群运行
- **技术栈**: Spark Core, Spark SQL, HDFS, 集群部署

## 环境要求

### 基础环境
- **Java**: JDK 8+
- **Scala**: 2.12.15
- **Apache Spark**: 3.3.0
- **SBT**: 1.5+

### 题目一额外要求
- **MySQL**: 8.0+
- **MySQL Connector/J**: 8.0.33

### 题目二额外要求
- **网络**: 确保9999端口可用

### 题目三额外要求
- **Hadoop**: 3.3.4
- **HDFS**: 分布式文件系统
- **Spark集群**: 独立调度器模式

## 快速开始

### 1. 环境准备
确保所有必需的服务都已启动：
- MySQL服务（题目一）
- HDFS集群（题目三）
- Spark集群（题目三）

### 2. 运行题目一
```bash
cd 一/
# 初始化MySQL数据库
mysql -u root -p < init_database.sql
# 编译运行
sbt run
```

### 3. 运行题目二
```bash
cd 二/
# 终端1：启动Socket数据源
sbt "runMain SocketDataSource"
# 终端2：启动Spark Streaming
sbt "runMain SparkStreamingDemo"
```

### 4. 运行题目三
```bash
cd 三/
# 初始化HDFS
chmod +x init_hdfs.sh && ./init_hdfs.sh
# 提交到Spark集群
chmod +x submit_spark.sh && ./submit_spark.sh
```

## 学习要点

### Spark核心概念
- **RDD**: 弹性分布式数据集
- **DataFrame**: 结构化数据抽象
- **Dataset**: 类型安全的数据抽象
- **Spark SQL**: SQL查询引擎

### 分布式计算特性
- **容错性**: 自动故障恢复
- **可扩展性**: 水平扩展计算能力
- **内存计算**: 提高处理速度
- **懒加载**: 优化执行计划

### 实际应用场景
- **批处理**: 大数据离线分析
- **流处理**: 实时数据处理
- **机器学习**: 大规模ML算法
- **图计算**: 社交网络分析

## 注意事项

1. **资源配置**: 根据数据量调整内存和CPU配置
2. **网络连接**: 确保各组件间网络通畅
3. **权限设置**: 检查HDFS和数据库访问权限
4. **版本兼容**: 注意Spark、Hadoop、Scala版本兼容性
5. **错误处理**: 程序包含完善的异常处理机制

## 扩展学习

- **Spark MLlib**: 机器学习库
- **Spark GraphX**: 图计算框架
- **Delta Lake**: 数据湖存储格式
- **Kubernetes**: 容器化部署Spark
- **云平台**: AWS EMR, Azure HDInsight等

## 联系信息
- **学号**: 220918
- **姓名**: 李佳颖
- **项目**: 分布式计算框架作业
